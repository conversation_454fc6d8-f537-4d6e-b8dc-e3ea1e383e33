#!/bin/bash

# 自动化性能测试脚本
# 用于CI/CD流水线中的性能回归检测

set -e

LOG_DIR="performance-logs"
RESULTS_FILE="$LOG_DIR/performance-results.json"
THRESHOLD_FILE="$LOG_DIR/performance-thresholds.json"

# 创建目录
mkdir -p "$LOG_DIR"

# 性能阈值配置
cat > "$THRESHOLD_FILE" << EOF
{
  "buildTime": {
    "warning": 120,
    "error": 150
  },
  "coldStart": {
    "warning": 100,
    "error": 130
  },
  "bundleSize": {
    "warning": 3000000,
    "error": 4000000
  }
}
EOF

echo "🚀 开始自动化性能测试..."

# 清理缓存
echo "🧹 清理缓存..."
rm -rf node_modules/.cache
rm -rf .cache
rm -rf dist
rm -rf build

# 测试构建时间
echo "📦 测试构建性能..."
BUILD_START=$(date +%s)
npm run build:prod > /dev/null 2>&1
BUILD_END=$(date +%s)
BUILD_TIME=$((BUILD_END - BUILD_START))

# 获取包大小
BUNDLE_SIZE=$(find dist -name "*.js" -type f -exec wc -c {} + | awk '{sum+=$1} END {print sum}')

# 生成结果JSON
cat > "$RESULTS_FILE" << EOF
{
  "timestamp": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
  "buildTime": $BUILD_TIME,
  "bundleSize": $BUNDLE_SIZE,
  "commit": "$(git rev-parse HEAD 2>/dev/null || echo 'unknown')",
  "branch": "$(git branch --show-current 2>/dev/null || echo 'unknown')"
}
EOF

# 读取阈值
BUILD_WARNING=$(cat "$THRESHOLD_FILE" | grep -o '"buildTime":{[^}]*"warning":[0-9]*' | grep -o '[0-9]*$')
BUILD_ERROR=$(cat "$THRESHOLD_FILE" | grep -o '"buildTime":{[^}]*"error":[0-9]*' | grep -o '[0-9]*$')

# 性能检查
echo "📊 性能检查结果:"
echo "构建时间: ${BUILD_TIME}s"
echo "包大小: $(($BUNDLE_SIZE / 1024 / 1024))MB"

# 检查是否超过阈值
EXIT_CODE=0

if [ "$BUILD_TIME" -gt "$BUILD_ERROR" ]; then
    echo "❌ 构建时间超过错误阈值 (${BUILD_ERROR}s)"
    EXIT_CODE=1
elif [ "$BUILD_TIME" -gt "$BUILD_WARNING" ]; then
    echo "⚠️  构建时间超过警告阈值 (${BUILD_WARNING}s)"
fi

if [ "$BUNDLE_SIZE" -gt 4000000 ]; then
    echo "❌ 包大小超过错误阈值 (4MB)"
    EXIT_CODE=1
elif [ "$BUNDLE_SIZE" -gt 3000000 ]; then
    echo "⚠️  包大小超过警告阈值 (3MB)"
fi

if [ "$EXIT_CODE" -eq 0 ]; then
    echo "✅ 性能测试通过"
else
    echo "❌ 性能测试失败"
fi

exit $EXIT_CODE
