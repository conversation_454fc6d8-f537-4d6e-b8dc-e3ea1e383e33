#!/bin/bash

# 优化效果测试脚本
# 用于测试构建速度优化前后的效果对比

LOG_FILE="performance-logs/optimization-test.log"
DATE=$(date '+%Y-%m-%d %H:%M:%S')

echo "🚀 开始构建速度优化效果测试..."

# 清理缓存确保公平测试
echo "🧹 清理缓存..."
rm -rf node_modules/.cache
rm -rf .cache
rm -rf dist
rm -rf build

# 测试生产构建时间
echo "📦 测试生产构建时间..."
BUILD_START=$(date +%s)
npm run build:prod
BUILD_END=$(date +%s)
BUILD_TIME=$((BUILD_END - BUILD_START))

# 记录结果
if [ ! -f "$LOG_FILE" ]; then
    echo -e "Date\tBuildTime(s)\tNotes" > "$LOG_FILE"
fi

echo -e "$DATE\t$BUILD_TIME\t优化后测试" >> "$LOG_FILE"

echo "✅ 测试完成！"
echo "📊 构建时间: ${BUILD_TIME}秒"
echo "📝 结果已记录到: $LOG_FILE"

# 显示最近几次的测试结果
echo ""
echo "📈 最近测试结果对比:"
tail -5 "$LOG_FILE"
