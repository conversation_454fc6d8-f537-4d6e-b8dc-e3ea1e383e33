#!/bin/bash

# 综合性能测试脚本
# 测试所有优化手段的综合效果

set -e

LOG_DIR="performance-logs"
RESULTS_FILE="$LOG_DIR/comprehensive-results.json"
DATE_STR=$(date '+%Y-%m-%d %H:%M:%S')

echo "🚀 开始综合性能测试..."

# 创建结果目录
mkdir -p "$LOG_DIR"

# 1. 预热 Webpack
echo "🔥 预热 Webpack..."
node scripts/webpack-warmup.js

# 2. 清理缓存进行冷启动测试
echo "🧹 清理缓存..."
rm -rf node_modules/.cache
rm -rf .cache
rm -rf dist
rm -rf build

# 3. 冷启动构建测试
echo "❄️  冷启动构建测试..."
COLD_START=$(date +%s)
npm run build:prod > /dev/null 2>&1
COLD_END=$(date +%s)
COLD_TIME=$((COLD_END - COLD_START))

# 4. 热启动构建测试
echo "🔥 热启动构建测试..."
HOT_START=$(date +%s)
npm run build:prod > /dev/null 2>&1
HOT_END=$(date +%s)
HOT_TIME=$((HOT_END - HOT_START))

# 5. 快速开发模式测试
echo "⚡ 快速开发模式测试..."
FAST_START=$(date +%s)
timeout 30s npm run start:fast > /dev/null 2>&1 || true
FAST_END=$(date +%s)
FAST_TIME=$((FAST_END - FAST_START))

# 6. 分析包大小
echo "📦 分析包大小..."
BUNDLE_SIZE=$(find dist -name "*.js" -type f -exec wc -c {} + | awk '{sum+=$1} END {print sum}')
CSS_SIZE=$(find dist -name "*.css" -type f -exec wc -c {} + | awk '{sum+=$1} END {print sum}')

# 7. 依赖分析
echo "🔍 依赖分析..."
node scripts/analyze-dependencies.js > /dev/null 2>&1

# 8. 生成综合报告
cat > "$RESULTS_FILE" << EOF
{
  "timestamp": "$DATE_STR",
  "buildTimes": {
    "coldStart": $COLD_TIME,
    "hotStart": $HOT_TIME,
    "fastDev": $FAST_TIME
  },
  "bundleSizes": {
    "javascript": $BUNDLE_SIZE,
    "css": $CSS_SIZE,
    "total": $((BUNDLE_SIZE + CSS_SIZE))
  },
  "optimizations": [
    "Webpack Resolve 优化",
    "Babel 配置优化",
    "样式处理缓存",
    "IgnorePlugin 减少模块",
    "快速开发模式",
    "ESLint 条件启用",
    "依赖分析工具",
    "Webpack 预热"
  ],
  "commit": "$(git rev-parse HEAD 2>/dev/null || echo 'unknown')",
  "branch": "$(git branch --show-current 2>/dev/null || echo 'unknown')"
}
EOF

# 9. 显示结果
echo ""
echo "📊 综合测试结果:"
echo "=================="
echo "冷启动构建: ${COLD_TIME}秒"
echo "热启动构建: ${HOT_TIME}秒"
echo "快速开发模式: ${FAST_TIME}秒"
echo "JavaScript 包大小: $(($BUNDLE_SIZE / 1024 / 1024))MB"
echo "CSS 包大小: $(($CSS_SIZE / 1024))KB"
echo "总包大小: $(((BUNDLE_SIZE + CSS_SIZE) / 1024 / 1024))MB"

# 10. 与历史数据对比
if [ -f "$LOG_DIR/cold-start.log" ]; then
    LAST_BUILD=$(tail -1 "$LOG_DIR/cold-start.log" | cut -f5)
    if [ ! -z "$LAST_BUILD" ] && [ "$LAST_BUILD" -gt 0 ]; then
        IMPROVEMENT=$(echo "scale=1; ($LAST_BUILD - $COLD_TIME) * 100 / $LAST_BUILD" | bc -l 2>/dev/null || echo "0")
        echo "构建时间改进: ${IMPROVEMENT}%"
    fi
fi

echo ""
echo "✅ 综合测试完成!"
echo "📄 详细报告: $RESULTS_FILE"
echo "📈 依赖分析: $LOG_DIR/dependency-analysis.json"
