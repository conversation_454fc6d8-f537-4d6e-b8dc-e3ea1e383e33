#!/bin/bash
# 自动化采集前端项目编译与启动性能
# 采集内容：cold start、start:1、start:2、build

set -e

LOG_DIR="performance-logs"
mkdir -p $LOG_DIR

COMPILE_KEYWORD="Compiled successfully"
DATE_STR=$(date '+%Y-%m-%d %H:%M:%S')

# 1. Cold Start
echo "Running: npm start"
START=$(date +%s)
npm start > $LOG_DIR/dev-server.log 2>&1 &
DEV_PID=$!
COLD_START_FILE="$LOG_DIR/.cold_start_time"
rm -f "$COLD_START_FILE"
(
  while IFS= read -r line; do
    if [[ "$line" == *"$COMPILE_KEYWORD"* ]]; then
      END=$(date +%s)
      echo $((END-START)) > "$COLD_START_FILE"
      kill $DEV_PID 2>/dev/null || true
      break
    fi
  done < <(tail -F $LOG_DIR/dev-server.log)
)
while [ ! -f "$COLD_START_FILE" ]; do sleep 1; done
COLD_START_TIME=$(cat "$COLD_START_FILE")

# 2. start:1
echo "Running: npm run start:1"
START=$(date +%s)
npm run start:1 > $LOG_DIR/start1-server.log 2>&1 &
START1_PID=$!
START1_FILE="$LOG_DIR/.start1_time"
rm -f "$START1_FILE"
(
  while IFS= read -r line; do
    if [[ "$line" == *"$COMPILE_KEYWORD"* ]]; then
      END=$(date +%s)
      echo $((END-START)) > "$START1_FILE"
      kill $START1_PID 2>/dev/null || true
      break
    fi
  done < <(tail -F $LOG_DIR/start1-server.log)
)
while [ ! -f "$START1_FILE" ]; do sleep 1; done
START1_TIME=$(cat "$START1_FILE")

# 3. start:2
echo "Running: npm run start:2"
START=$(date +%s)
npm run start:2 > $LOG_DIR/start2-server.log 2>&1 &
START2_PID=$!
START2_FILE="$LOG_DIR/.start2_time"
rm -f "$START2_FILE"
(
  while IFS= read -r line; do
    if [[ "$line" == *"$COMPILE_KEYWORD"* ]]; then
      END=$(date +%s)
      echo $((END-START)) > "$START2_FILE"
      kill $START2_PID 2>/dev/null || true
      break
    fi
  done < <(tail -F $LOG_DIR/start2-server.log)
)
while [ ! -f "$START2_FILE" ]; do sleep 1; done
START2_TIME=$(cat "$START2_FILE")

# 4. Build
echo "Running: npm run build:prod"
START=$(date +%s)
npm run build:prod > $LOG_DIR/build-server.log 2>&1
END=$(date +%s)
BUILD_TIME=$((END-START))

# 5. 输出表格
COLD_START_LOG=$LOG_DIR/cold-start.log
if [ ! -s "$COLD_START_LOG" ]; then
  echo -e "Date\tColdStart(s)\tStart1(s)\tStart2(s)\tBuild(s)" > $COLD_START_LOG
fi
echo -e "$DATE_STR\t$COLD_START_TIME\t$START1_TIME\t$START2_TIME\t$BUILD_TIME" >> $COLD_START_LOG

# 6. 自动清理中间日志和临时文件
rm -f $LOG_DIR/dev-server.log $LOG_DIR/start1-server.log $LOG_DIR/start2-server.log $LOG_DIR/build-server.log
rm -f "$COLD_START_FILE" "$START1_FILE" "$START2_FILE" 