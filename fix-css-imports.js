#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// 递归查找所有 .js 和 .jsx 文件
function findJSFiles(dir, fileList = []) {
  const files = fs.readdirSync(dir);

  files.forEach((file) => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);

    if (stat.isDirectory()) {
      // 跳过 node_modules 和其他不需要的目录
      if (!['node_modules', '.git', 'dist', 'build'].includes(file)) {
        findJSFiles(filePath, fileList);
      }
    } else if (file.endsWith('.js') || file.endsWith('.jsx')) {
      fileList.push(filePath);
    }
  });

  return fileList;
}

// 修复单个文件中的CSS模块导入
function fixCSSImportsInFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');

    // 匹配多种CSS模块导入模式
    const patterns = [
      // import * as styles from '*.module.less'
      // import * as styles from "*.module.less"
      /import\s+\*\s+as\s+(\w+)\s+from\s+['"]([^'"]*\.module\.(less|css))['"];?/g,

      // import * as styles from './style.module.less'
      // import * as styles from "../style.module.less"
      /import\s+\*\s+as\s+(\w+)\s+from\s+['"]([^'"]*\.module\.(less|css))['"];?/g,
    ];

    let hasChanges = false;
    let newContent = content;

    patterns.forEach((regex) => {
      newContent = newContent.replace(regex, (match, variableName, modulePath, extension) => {
        hasChanges = true;
        console.log(`  修复: ${match} -> import ${variableName} from '${modulePath}';`);
        return `import ${variableName} from '${modulePath}';`;
      });
    });

    if (hasChanges) {
      fs.writeFileSync(filePath, newContent, 'utf8');
      return true;
    }

    return false;
  } catch (error) {
    console.error(`处理文件 ${filePath} 时出错:`, error.message);
    return false;
  }
}

// 主函数
function main() {
  const srcDir = path.join(process.cwd(), 'src');

  if (!fs.existsSync(srcDir)) {
    console.error('错误: 找不到 src 目录');
    process.exit(1);
  }

  console.log('🔍 正在查找所有 JavaScript 文件...');
  const jsFiles = findJSFiles(srcDir);
  console.log(`📁 找到 ${jsFiles.length} 个 JavaScript 文件`);

  console.log('\n🔧 开始修复CSS模块导入...');
  let fixedFiles = 0;
  let totalChanges = 0;

  jsFiles.forEach((filePath) => {
    const relativePath = path.relative(process.cwd(), filePath);

    // 检查文件是否包含CSS模块导入
    const content = fs.readFileSync(filePath, 'utf8');
    if (content.includes('import * as') && content.includes('.module.')) {
      console.log(`\n📝 处理文件: ${relativePath}`);
      const hasChanges = fixCSSImportsInFile(filePath);
      if (hasChanges) {
        fixedFiles++;
        totalChanges++;
      }
    }
  });

  console.log('\n✅ 修复完成!');
  console.log('📊 统计信息:');
  console.log(`   - 总文件数: ${jsFiles.length}`);
  console.log(`   - 修复文件数: ${fixedFiles}`);
  console.log(`   - 总修改数: ${totalChanges}`);

  if (fixedFiles > 0) {
    console.log('\n🎉 CSS模块导入语法已批量修复!');
    console.log('💡 建议现在运行 npm run qa 来验证修复效果');
  } else {
    console.log('\n📝 没有找到需要修复的CSS模块导入');
  }
}

// 运行脚本
if (require.main === module) {
  main();
}

module.exports = { findJSFiles, fixCSSImportsInFile };
