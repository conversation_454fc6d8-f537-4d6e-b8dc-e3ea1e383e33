#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// 递归查找所有 .js 和 .jsx 文件
function findJSFiles(dir, fileList = []) {
  const files = fs.readdirSync(dir);
  
  files.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat.isDirectory()) {
      // 跳过 node_modules 和其他不需要的目录
      if (!['node_modules', '.git', 'dist', 'build'].includes(file)) {
        findJSFiles(filePath, fileList);
      }
    } else if (file.endsWith('.js') || file.endsWith('.jsx')) {
      fileList.push(filePath);
    }
  });
  
  return fileList;
}

// 检查文件中的CSS模块导入
function checkCSSImportsInFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    
    // 检查各种可能的CSS模块导入模式
    const patterns = [
      // 已修复的模式：import styles from '*.module.less'
      /import\s+(\w+)\s+from\s+['"]([^'"]*\.module\.(less|css))['"];?/g,
      
      // 未修复的模式：import * as styles from '*.module.less'
      /import\s+\*\s+as\s+(\w+)\s+from\s+['"]([^'"]*\.module\.(less|css))['"];?/g,
      
      // 其他可能的模式
      /import\s+{\s*(\w+)\s*}\s+from\s+['"]([^'"]*\.module\.(less|css))['"];?/g,
    ];
    
    const results = {
      fixed: [],
      unfixed: [],
      other: []
    };
    
    // 检查已修复的模式
    let match;
    while ((match = patterns[0].exec(content)) !== null) {
      results.fixed.push({
        type: 'fixed',
        match: match[0],
        variable: match[1],
        path: match[2]
      });
    }
    
    // 重置regex
    patterns[0].lastIndex = 0;
    
    // 检查未修复的模式
    while ((match = patterns[1].exec(content)) !== null) {
      results.unfixed.push({
        type: 'unfixed',
        match: match[0],
        variable: match[1],
        path: match[2]
      });
    }
    
    // 重置regex
    patterns[1].lastIndex = 0;
    
    // 检查其他模式
    while ((match = patterns[2].exec(content)) !== null) {
      results.other.push({
        type: 'other',
        match: match[0],
        variable: match[1],
        path: match[2]
      });
    }
    
    return results;
  } catch (error) {
    console.error(`处理文件 ${filePath} 时出错:`, error.message);
    return null;
  }
}

// 主函数
function main() {
  const srcDir = path.join(process.cwd(), 'src');
  
  if (!fs.existsSync(srcDir)) {
    console.error('错误: 找不到 src 目录');
    process.exit(1);
  }
  
  console.log('🔍 正在检查CSS模块导入状态...');
  const jsFiles = findJSFiles(srcDir);
  console.log(`📁 找到 ${jsFiles.length} 个 JavaScript 文件`);
  
  let totalFixed = 0;
  let totalUnfixed = 0;
  let totalOther = 0;
  let filesWithUnfixed = [];
  let filesWithOther = [];
  
  jsFiles.forEach(filePath => {
    const results = checkCSSImportsInFile(filePath);
    if (!results) return;
    
    const relativePath = path.relative(process.cwd(), filePath);
    
    if (results.fixed.length > 0) {
      totalFixed += results.fixed.length;
    }
    
    if (results.unfixed.length > 0) {
      totalUnfixed += results.unfixed.length;
      filesWithUnfixed.push({
        file: relativePath,
        imports: results.unfixed
      });
    }
    
    if (results.other.length > 0) {
      totalOther += results.other.length;
      filesWithOther.push({
        file: relativePath,
        imports: results.other
      });
    }
  });
  
  console.log('\n📊 CSS模块导入统计:');
  console.log(`   ✅ 已修复: ${totalFixed} 个`);
  console.log(`   ❌ 未修复: ${totalUnfixed} 个`);
  console.log(`   ❓ 其他模式: ${totalOther} 个`);
  
  if (filesWithUnfixed.length > 0) {
    console.log('\n❌ 仍需修复的文件:');
    filesWithUnfixed.forEach(({ file, imports }) => {
      console.log(`\n📝 ${file}:`);
      imports.forEach(imp => {
        console.log(`   - ${imp.match}`);
      });
    });
  }
  
  if (filesWithOther.length > 0) {
    console.log('\n❓ 其他导入模式的文件:');
    filesWithOther.forEach(({ file, imports }) => {
      console.log(`\n📝 ${file}:`);
      imports.forEach(imp => {
        console.log(`   - ${imp.match}`);
      });
    });
  }
  
  if (totalUnfixed === 0 && totalOther === 0) {
    console.log('\n🎉 所有CSS模块导入都已正确修复!');
  }
}

// 运行脚本
if (require.main === module) {
  main();
}

module.exports = { findJSFiles, checkCSSImportsInFile };
