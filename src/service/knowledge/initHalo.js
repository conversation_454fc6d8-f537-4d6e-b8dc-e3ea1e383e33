/* eslint-disable class-methods-use-this */
import * as halo from '@didi/halo-client-web/halo_client_web.js';
import { getCookie, inPhone } from '@/utils';
// WebAssembly memory access - webpack5 compatible approach
let wasmMemory;

// Registry 用以发现 halo 服务端结点，每次链接 halo websocket 之前，都需要请求 halo registry，
// 获取当前可用的 halo 结点地址


let HALO_REGISTRY = null;
if (['dev', 'test'].indexOf(process.env.APP_ENV) !== -1) {
  // HALO_REGISTRY = 'https://api-kylin-xg02.intra.xiaojukeji.com/dichat_halo_registry_test_test/get_halo_info';
  HALO_REGISTRY = 'wss://qa-rtm-cooper.intra.xiaojukeji.com:8002';
}
if (process.env.APP_ENV === 'qa') {
  HALO_REGISTRY = 'wss://qa-rtm-cooper.intra.xiaojukeji.com:8002';
}
if (process.env.APP_ENV === 'prod') {
  HALO_REGISTRY = 'wss://rtm-cooper.xiaojukeji.com:80';
}


// Rust Side:
// pub enum ClientState {
//     None = 0,
//     Init = 1,
//     Connecting = 2,
//     Connected = 3,
//     Disconnected = 4,
//     ConnectErr = 5,
// }
export const { ClientState } = halo;

// Rust Side:
// pub enum RegistryEvent {
//     Requesting = 0,
//     RequestSuccess = 1,
//     RequestFailure = 2,
// }
export const { RegistryEvent } = halo;

class HaloClient {
  constructor() {
    // halo 实例，可以用的函数签名可以参考 ./node_modules/@didi/halo-client-web/halo_client_web.d.ts
    this.halo = halo;
    this.isOpened = false;
    this.haloState = ClientState.None;
    // 缓存需要监听的事件
    this.eventMap = {};

    // 必须先执行此初始化函数
    halo.init_wasm();

    this.handleStateChanged = this.handleStateChanged.bind(this);
    this.handleMessage = this.handleMessage.bind(this);
    this.sendMessage = this.sendMessage.bind(this);
    this.handleRegistryEvent = this.handleRegistryEvent.bind(this);
    this.connect = this.connect.bind(this);
    this.close = this.close.bind(this);
  }

  connect() {
    if (
      [ClientState.Connected, ClientState.Connecting].includes(this.haloState)
    ) { return; }

    // 链接状态变化的回调函数，用于相应 halo client state
    // 的变化，参数为 ClientState 枚举
    this.halo.set_state_changed(this.handleStateChanged);

    // 收到新的业务消息时的回调函数
    this.halo.set_receive_message(this.handleMessage);

    // 请求 registry 也会触发相应的 Registry Event，用以使业务层可以处理 registry 请求失败等情况
    this.halo.set_on_registry_event(this.handleRegistryEvent);

    // client info 会被透明传输至 registry 服务，registry 会根据不同字段值进行客户端身份区分，
    // 可能返回不同的 halo 结点
    // this.halo.set_client_info({
    //   app_version: 'v1.0.0',
    //   uid: '1',
    //   username: 'nobody',
    // });

    // 触发链接
    this.isOpened = this.halo.open_connection_with_static_address(HALO_REGISTRY);
  }

  handleStateChanged(state, error) {
    switch (state) {
      case ClientState.Init:
        break;
      case ClientState.Disconnected:
        break;
      case ClientState.Connected:
        // 如果DC内访问使用X-User-Token
        const ua = navigator.userAgent.toLowerCase();
        const token = (inPhone() && ua.indexOf('zhushou') !== -1) ? JSON.stringify({
          _user_token_dc: getCookie('X-User-Token') || getCookie('_chain_user_token') || getCookie('_user_token') || window._dc_token,
        }) : JSON.stringify({
          _user_token: getCookie('_chain_user_token') || getCookie('_user_token'),
        })
        console.log('****send-message***meta', token, getCookie('X-User-Token'), window._dc_token)
        this.sendMessage({
          namespace: 'cooper',
          path: '/open/connection',
          bytes: new Uint8Array(),
          meta: JSON.stringify({
            _user_token: getCookie('_chain_user_token') || getCookie('_user_token'),
          }),
        });
        this.sendMessage({
          namespace: 'coo-message',
          path: '/open/connection',
          bytes: new Uint8Array(),
          meta: token,
        });
        break;
      case ClientState.ConnectErr:
        break;
      case ClientState.Connecting:
        break;
      default:
    }

    this.halo.set_state_changed(this.handleStateChanged);

    this.haloState = state;
  }

  handleRegistryEvent(event, error) {
    switch (event) {
      case RegistryEvent.Requesting:
        break;
      case RegistryEvent.RequestSuccess:
        break;
      case RegistryEvent.RequestFailure:
        break;
      default:
        console.warn(`[halo-client][Registry]Unhandled halo registry event: ${event}`);
    }
  }

  sendMessage({
    namespace, path, bytes, meta = '{}',
  }) {
    this.halo.send_message(
      // halo 是业务无关的长链接，不同的业务可以同时运行在一个 socket 链接上，不同的业务由不同的 namespace 和 path 来区分
      namespace, // string
      path, // string
      // bytes 要发送的消息体，在 js 侧需要转换成 Uini8 Array 数据结构，传递给 WebAssembly 侧
      // 而具体的 封包/解包方式，由业务服务自行约定，例如：
      // 1. `const bytes = new TextEncoder().encode(JSON.stringify({ content: 1 }));`
      // 2. `const bytes = require('msgpack-lite').encode(message);
      bytes,
      // meta 为 json 字符串，是业务消息体可携带的元信息字段
      meta,
    );
  }

  handleMessage(namespace, path, bodyOffset, bodyLength, meta) {
    let parsedMeta = null;

    try {
      parsedMeta = meta ? JSON.parse(meta) : {};
    } catch (e) {
      console.warn('[halo-client]Parse halo message meta failed', {
        meta,
      });
    }

    // 收到消息时，webassmbly 侧会将新消息在共享内存中的偏移值和长度返回出来，
    // js 侧先构建正 Uinit8Array，再自行进行解包
    // 获取WebAssembly内存 - webpack5兼容方式
    if (!wasmMemory) {
      try {
        // 尝试多种方式获取WebAssembly内存
        wasmMemory = halo.memory
                    || (halo.__wbg_memory_get && halo.__wbg_memory_get())
                    || (halo.wasm && halo.wasm.memory)
                    || (window.__wbg_memory_get && window.__wbg_memory_get())
                    || (global.__wbg_memory_get && global.__wbg_memory_get());

        // 如果还是没有找到，尝试从全局变量获取
        if (!wasmMemory && typeof WebAssembly !== 'undefined') {
          // 创建一个临时的内存实例作为fallback
          wasmMemory = new WebAssembly.Memory({ initial: 256, maximum: 256 });
        }
      } catch (error) {
        console.warn('Failed to get WebAssembly memory:', error);
        // 创建一个fallback内存
        wasmMemory = new WebAssembly.Memory({ initial: 256, maximum: 256 });
      }
    }

    let msgBytes;
    try {
      msgBytes = new Uint8Array(wasmMemory.buffer, bodyOffset, bodyLength);
    } catch (error) {
      console.error('Failed to create message bytes array:', error);
      // 创建一个空的字节数组作为fallback
      msgBytes = new Uint8Array(0);
    }

    let bytesString = new TextDecoder().decode(msgBytes);

    this.emit(JSON.parse(bytesString).type, {
      parsedMeta,
      bytesObj: JSON.parse(bytesString) || {},
      path,
      namespace,
    });
  }

  close() {
    this.halo.close_connection();
    this.haloState = ClientState.None;
  }

  hasOpen() {
    return this.isOpened;
  }

  addEventListener(eventType, fn) {
    if (!this.eventMap[eventType]) {
      this.eventMap[eventType] = [];
    }
    this.eventMap[eventType].push(fn);
    return this;
  }

  removeEventListener(eventType, fn) {
    let fns = this.eventMap[eventType];
    // 如果缓存列表中没有相应的 fn，返回false
    if (!fns) return false;
    if (!fn) {
      // 如果没有传 fn 的话，就会将 event 值对应缓存列表中的 fn 都清空
      fns && (fns.length = 0);
    } else {
      // 若有 fn，遍历缓存列表，看看传入的 fn 与哪个函数相同，如果相同就直接从缓存列表中删掉即可
      for (let i = 0, total = fns.length; i < total; i++) {
        fns[i] === fn && fns.splice(i, 1);
      }
    }
    return this;
  }

  emit(eventType, ...args) {
    const fns = this.eventMap[eventType];

    // 如果缓存列表里没有 fn 就返回 false
    if (!fns || fns.length === 0) {
      return false;
    }
    // 遍历 event 值对应的缓存列表，依次执行 fn
    fns.forEach((fn) => {
      fn.apply(this, args);
    });
    return this;
  }
}
const haloClient = new HaloClient();
haloClient.connect()

export default haloClient;

