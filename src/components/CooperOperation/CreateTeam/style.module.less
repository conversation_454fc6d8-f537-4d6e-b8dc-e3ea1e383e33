.create-team-content {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  position: relative;

  &-left {
    width: 500px;
    position: relative;
    padding: 32px 0;
    height: 100%;

    .title {
      font-family: PingFangSC-Semibold;
      font-size: 18px;
      font-weight: normal;
      color: @blueGray-color;
      height: 26px;
      line-height: 26px;
      margin-bottom: 16px;
      padding-left: 32px;
    }

    .create-team-content-left-body {
      padding: 0 32px;
      height: 380px;
      overflow: hidden;
      overflow-y: auto;
    }

    .team-name,
    .team-type,
    .team-permis,
    .team-space-type,
    .team-add-member {
      >p {
        font-size: 14px;
        font-weight: normal;
        line-height: 22px;
        font-family: PingFangSC-Medium;
        height: 22px;
        margin-bottom: 8px;
      }
    }

    .team-name {
      margin-bottom: 18px;
      height: 76px;

      >span {
        margin-top: 2px;
        color: #FF563B;
        font-size: 12px;
        display: flex;
        align-items: center;
      }

      .cuowu-tip {
        font-size: 14px;
        padding-right: 2px;
      }

      .tips {
        display: flex;
        align-items: center;
        color: #FF563B;
        font-size: 12px;

        .tips-left {
          max-width: 180px;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }


        a {
          max-width: 180px;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          padding-left: 4px;
          color: #06f,
        }
      }

      .icon-error {
        font-size: 14px;
      }

      .team-name-input {
        border-radius: 4px !important;
        padding: 2px 11px !important;

        &:hover {
          border-color: #1A6EFF !important;
        }

        :global {
          .ant-input-suffix {
            visibility: hidden;
          }

          input.ant-input:focus {
            border: none !important
          }
        }

        &:hover,
        &:focus-within {
          :global {
            .ant-input-suffix {
              visibility: visible;
            }
          }
        }
      }
    }

    .team-type {

      margin-bottom: 20px;

      >div {
        >div {
          margin-bottom: 6px;
          line-height: 22px;
          font-size: 14px;
          font-family: PingFangSC-Regular;
          display: block;
          overflow: hidden;
          width: 100%;
          height: 22px;

          .label {
            color: @blueGray-color;
            margin-right: 8px;
          }

          .des {
            font-size: 12px;
            color: #6A707C;
          }
        }

        .disabled {
          cursor: not-allowed;

          .label {
            color: #909499;
          }

          :global {
            .ant-radio-wrapper:hover {
              .ant-radio-inner {
                border-color: rgba(34, 42, 53, 0.08) !important;
              }
            }
          }
        }
      }
    }

    .team-space-type {
      margin-bottom: 20px;

      &-box {
        display: flex;
        justify-content: space-between;

        >div {
          border: 1px solid @blueGray-10;
          padding: 8px 13px;
          display: flex;
          align-items: flex-start;
          border-radius: 4px;
          width: 50%;
          cursor: pointer;

          &:last-child {
            margin-left: 10px;
          }

          >div {
            margin-left: 4px;

            >span {
              display: block;
            }

            .label {
              font-size: 14px;
              color: @blueGray-1;
            }

            .des {
              line-height: 18px;
              color: @blueGray-15;
              font-size: 12px;
              word-break: break-all
            }
          }
        }

        .div-active {
          border: 1px solid #1A6EFF;
        }
      }
    }

    .required {
      >p {
        position: relative;

        &::after {
          content: '*';
          font-size: 16px;
          color: #ff3f5a;
          position: absolute;
          // left: 60px;
          top: 2px;
          margin-left: 4px;
        }
      }

    }

    .team-permis {
      margin-bottom: 20px;

      :global {
        .dropdown-checkbox {
          width: 100% !important;
          height: 36px !important;

          .dropdown-checkbox__value .dropdown-checkbox__caret {
            height: 36px !important;
          }
        }

      }

      >p {
        &::after {
          content: '*';
          font-size: 16px;
          color: #ff3f5a;
          position: absolute;
          left: 87px;
          top: 2px;
        }
      }
    }

    .team-module {
      >p {
        font-size: 14px;
        font-weight: normal;
        line-height: 22px;
        font-family: PingFangSC-Medium;
        height: 22px;
        margin-bottom: 8px
      }

      .team-module-content {
        margin-bottom: 16px;

        :global {
          .ant-checkbox-wrapper+.ant-checkbox-wrapper {
            margin-left: 0;
          }

          .ant-checkbox-wrapper {
            margin-bottom: 6px;
          }
        }
      }

      &-item-label {
        color: rgba(0, 0, 0, 0.9);
        margin: 0 8px 0 0;
      }

      &-item-desc {
        font-size: 12px;
        color: @blueGray-15
      }
    }

    .team-add-member {
      .ant-select {
        height: 36px !important;
      }

      :global {
        input {
          &:focus {
            border: none !important;
          }
        }
      }
    }

    .team-private {
      cursor: pointer;
    }

    .team-public {
      cursor: pointer;
    }
  }

  .action {
    position: absolute;
    left: 32px;
    bottom: 32px;

    .cancel {
      margin-right: 8px;
    }
  }

  &-right {
    width: 300px;
    height: 100%;
  }

}

.modal-content {
  >p {
    font-size: 14px;
    font-weight: 400;
    font-family: PingFang SC;
    line-height: 22px;
    color: #4E555D;

    &:first-child {
      margin-bottom: 20px;
    }

    span {
      cursor: pointer;
      color: #1A6EFF;
      font-weight: 500;

      &:hover {
        text-decoration: underline;
      }
    }

  }
}

.permission-tip {
  width: 20px;
  height: 20px;
  line-height: 20px !important;
  text-align: center;
  color: @blueGray-6;
  cursor: pointer;
  margin-left: 2px;
  font-size: 14px !important;
  display: inline-block;

  &:hover {
    color: @blueGray-1;
    background-color: #E8E9EA;
    border-radius: 4px;
  }
}

.out-team-space-details {
  color: #222A35;
  font-size: 14;

  >span {
    color: #1A6EFF;
    cursor: pointer;
    font-weight: 500;
  }
}