import React, { useEffect, useState, useMemo } from "react";
import { intl } from "di18n-react";
import classNames from "classnames/bind";
import DropdownRadio from "@/components/common/DropdownRadio";
import { formatDatetimeNew } from "@/utils/cooperutils";
import styles from './style.module.less';
import { modifyShareLink } from "@/service/knowledge/share";
import { modifyShareLinkCooper } from "@/service/cooper/home";
import {
  PERM_DEFAULT_LIST_KNOWLEDGE,
  PERM_DEFAULT_LIST_SHARE,
} from "@/components/CooperFoldAuth/contants";
import { IN_OUT } from "@/constants/space";
import { message, Checkbox, Select } from "antd";
import { sendFileTypeEvent } from "@/utils/type";
import {
  permissionOptions,
  accessingLinkOption,
  permissionOptionsDoc,
} from "./config";
import { getLocale } from "di18n-react";
import {PERIOD_MAP} from '../constants'

const cx = classNames.bind(styles);

const NewLinkShareSetting = (props) => {
  const {
    isDkPage = true,
    linkInfo = {},
    refreshLinkInfo,
    info,
    disabled,
  } = props;
  const {
    accessRole,
    permission,
    expire_time,
    password,
    expiration,
    access_type,
    resource_id,
    invite_link_id,
  } = linkInfo;

  const [newPassword, setPassword] = useState(password); // 新密码
  const [needPassword, setNeedPassword] = useState(access_type === "Secret");

  const isEN = getLocale() === "en-US";

  useEffect(() => {
    setPassword(password);
  }, [password]);

  useEffect(() => {
    setNeedPassword(access_type === "Secret");
  }, [access_type]);

  const generatePwd = () => {
    return Math.floor(100000 + Math.random() * 900000);
  };

  const DkpermOptions = useMemo(() => {
    return isDkPage ? permissionOptions() : permissionOptionsDoc();
  }, isDkPage);

  const disabledTitle = useMemo(() => {
    return disabled ? intl.t("仅文档管理员可修改设置") : "";
  }, [disabled]);

  const getPerm = (values) => {
    const prem = values.reduce((sum, item) => sum + item, 0);
    return prem;
  };

  // 处理密码变更
  const handleChangePwd = (value, _newPassword) => {
    const isNeedPassword = value === "Secret";
    const omegaKey = isNeedPassword
      ? "ep_share_link_manage_password_ck"
      : "ep_share_link_manage_unpassword_ck";
    sendFileTypeEvent(omegaKey, info?.fileType);
    setPassword(_newPassword);
    setNeedPassword(isNeedPassword);
  };

  // change
  const handleChange = (changekey, value) => {
    const params = {
      resource_id,
      invite_link_id,
      permission: permission,
      password: newPassword,
      expiration: expiration || 3,
      accessRole,
      access_type: needPassword ? "Secret" : "Public",
      linkSelectType: 1,
    };
    params[changekey] = value;
    // 密码特殊处理
    if (changekey === "access_type") {
      const _newPassword = generatePwd();
      params.password = _newPassword;
      handleChangePwd(value, _newPassword);
    }
    const fn = isDkPage ? modifyShareLink : modifyShareLinkCooper;
    fn(params).then(() => {
      message.success(intl.t("修改成功"));
      refreshLinkInfo();
    });
  };

  const resetPwd = () => {
    const _newPassword = generatePwd();
    setPassword(_newPassword);
    handleChange("password", _newPassword);
    sendFileTypeEvent(
      "ep_share_link_manage_passwordreset_ck",
      info?.fileType
    );
  };
  const permsValue = useMemo(() => {
    let defaultperm = [];
    const permMap = isDkPage
      ? PERM_DEFAULT_LIST_KNOWLEDGE
      : PERM_DEFAULT_LIST_SHARE;
    for (let i = 0; i < permMap.length; i++) {
      if (permission & permMap[i]) {
        defaultperm.push(DkpermOptions[i].value);
      }
    }
    return defaultperm;
  }, [permission, isDkPage, DkpermOptions]);
  return (
    <div className={cx("share-content")}>
      <div className={cx("share-link-setting")}>
        <span style={{ width: isEN ? "86px" : "56px" }}>
          {intl.t("链接权限p")}
        </span>
        <Checkbox.Group
          options={DkpermOptions}
          defaultValue={permsValue}
          onChange={(values) => {
            handleChange("permission", getPerm(values));
            sendFileTypeEvent(
              "ep_share_link_manage_permission_ck",
              info?.fileType
            );
          }}
        />
      </div>
      <div className={cx("share-link-setting")}>
        <span
          className={cx("link-setting-label")}
          style={{ width: isEN ? "86px" : "56px" }}
        >
          {intl.t("链接期限")}
        </span>
        <Select
          defaultValue={expiration || 3}
          style={{ width: 104, height: 30, marginRight: 10 }}
          onChange={(value) => {
            handleChange("expiration", value);
            sendFileTypeEvent(
              "ep_share_link_manage_modifyperiod_ck",
              info?.fileType,
              {
                period: PERIOD_MAP[value]
              }
            );
          }}
          dropdownStyle={{width: "auto"}}
          dropdownClassName="share-link-expiration-drop"
          options={[
            {
              value: 1,
              label: intl.t("永久有效"),
              disabled: (info?.relationTypeTags || []).includes(IN_OUT),
            },

            {
              value: 2,
              label: `1${intl.t("个月1")}`,
            },

            {
              value: 3,
              label: `1${intl.t("周1")}`,
            },

            {
              value: 4,
              label: `1${intl.t("天1")}`,
            },
          ]}
        />
        {expiration !== 1 && !isEN && (
          <span>
            {formatDatetimeNew(expire_time, intl.t("MM月DD日 HH:mm"))}
            {intl.t("过期后，重置为仅协作者可见")}
          </span>
        )}
      </div>
      {expiration !== 1 && isEN && (
        <div className={cx("share-link-expiration-en-tip")}>
          {formatDatetimeNew(expire_time, intl.t("MM月DD日 HH:mm"))}
          {intl.t("过期后，重置为仅协作者可见")}
        </div>
      )}
      {needPassword && (
        <div className={cx("share-link-setting")}>
          <span
            className={cx("link-setting-password")}
            style={{ width: isEN ? "86px" : "56px" }}
          >
            {intl.t("密码设置")}
          </span>
          <span className={cx("password-box")}>{password}</span>
          <div className={cx("setting-password-reset")} onClick={resetPwd}>
            {intl.t("重置")}
          </div>
        </div>
      )}
      <div className={cx("share-link-setting")}>
        <span style={{ width: isEN ? "86px" : "56px" }}>
          {intl.t("协作设置")}
        </span>
        <span>{intl.t("通过链接访问的人")}</span>
        <DropdownRadio
          options={accessingLinkOption()}
          onChange={(value) => {
            handleChange("accessRole", value);
            const omegaKey = !!value
              ? "ep_share_link_manage_uninvite_ck"
              : "ep_share_link_manage_invite_ck";
            sendFileTypeEvent(omegaKey, info?.fileType);
          }}
          value={accessRole}
          isShareDropdownRadio={true}
        />
      </div>
    </div>
  );
};

export default NewLinkShareSetting;
