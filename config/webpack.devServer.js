const paths = require('./paths');
const ignoredFiles = require('react-dev-utils/ignoredFiles');


module.exports = {
  // webpack-dev-server v4+ 配置
  allowedHosts: 'all', // 替代 disableHostCheck
  compress: true,
  client: {
    logging: 'none', // 替代 clientLogLevel
    overlay: false,
  },
  static: {
    directory: paths.appBuild, // 替代 contentBase
    watch: true, // 替代 watchContentBase
  },
  hot: true,
  // webSocketServer: 'ws', // 替代 transportMode
  host: '0.0.0.0',
  historyApiFallback: true,
  // 接口代理配置
  // `proxy` is run between `before` and `after` `webpack-dev-server` hooks
  proxy: [{
    context: ['/cooper_gateway', '/platform/stream', '/v1/resource', '/didocapi', '/jotting', 'cooper_server'],
    target: process.env.APP_ENV === 'qa' ? 'https://cooper-qa.didichuxing.com' : 'https://cooper-test.didichuxing.com',
    changeOrigin: true,
  }, {
    context: ['/collab'],
    target: 'https://didoc-qa.didichuxing.com',
    changeOrigin: true,
    secure: false,
    cookieDomainRewrite: 'localhost',
  }, {
    context: ['/api'],
    target: 'https://didoc-qa.didichuxing.com',
    changeOrigin: true,
    secure: false,
    cookieDomainRewrite: 'localhost',
    pathRewrite: { '^/api/api/': '/api/' },
  }, {
    context: ['/collab/ws'],
    target: 'https://didoc-qa.didichuxing.com',
    changeOrigin: true,
    secure: false,
    cookieDomainRewrite: 'localhost',
    ws: true,
  }],
  headers: {
    'Access-Control-Allow-Origin': '*',
  },
  // webpack-dev-server v4+ 使用 setupMiddlewares
  setupMiddlewares: (middlewares, devServer) => {
    // 在这里可以添加自定义中间件
    // middlewares 是中间件数组，可以在前面或后面添加自定义中间件
    return middlewares;
  },
};
