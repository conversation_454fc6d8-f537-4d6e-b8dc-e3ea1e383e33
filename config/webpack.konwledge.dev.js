const paths = require('./paths');
const { merge } = require('webpack-merge');
const common = require('./webpack.common');
const CaseSensitivePathsPlugin = require('case-sensitive-paths-webpack-plugin');
const ReactRefreshWebpackPlugin = require('@pmmmwh/react-refresh-webpack-plugin');

const webpackDevClientEntry = require.resolve('react-dev-utils/webpackHotDevClient');
const reactRefreshOverlayEntry = require.resolve('react-dev-utils/refreshOverlayInterop');
// WatchMissingNodeModulesPlugin 已被 Webpack 5 内置功能替代
const HtmlWebpackPlugin = require('html-webpack-plugin');

const shouldUseSourceMap = process.env.GENERATE_SOURCEMAP !== 'false';


module.exports = [
  merge(common, {
    mode: 'development',
    bail: false,
    devtool: shouldUseSourceMap ? 'cheap-module-source-map' : false,
    entry: [webpackDevClientEntry, paths.knowledgeAppIndexJs],
    output: {
      // TODO: path
      path: paths.appBuild,
      pathinfo: true,
      filename: 'static/js/[name].js',
      chunkFilename: 'static/js/[name].chunk.js',
      publicPath: '/',
    },
    // Webpack 5 内置的 watch 配置，替代 WatchMissingNodeModulesPlugin
    watchOptions: {
      ignored: /node_modules/,
      aggregateTimeout: 300,
      poll: 1000,
    },
    plugins: [
      new HtmlWebpackPlugin({
        template: paths.knowledgeHtml,
      }),
      new CaseSensitivePathsPlugin(),
      new ReactRefreshWebpackPlugin({
        overlay: {
          entry: webpackDevClientEntry,
          module: reactRefreshOverlayEntry,
          sockIntegration: false,
        },
      }),
    ],
  }),

];
