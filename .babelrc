{"presets": ["@babel/preset-react", ["@babel/preset-env", {"modules": false, "debug": false, "corejs": 3, "useBuiltIns": "usage", "targets": {"browsers": ["> 1%", "last 2 versions", "not ie <= 8"]}}]], "plugins": [["import", {"libraryName": "antd", "libraryDirectory": "es", "style": true}], ["@babel/plugin-transform-runtime", {"corejs": 3, "helpers": true, "regenerator": true, "useESModules": false}], "@babel/plugin-syntax-dynamic-import", "@babel/plugin-proposal-class-properties", "@babel/plugin-proposal-nullish-coalescing-operator", "@babel/plugin-proposal-optional-chaining"]}