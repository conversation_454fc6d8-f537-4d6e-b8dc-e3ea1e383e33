{"presets": ["@babel/preset-react", ["@babel/preset-env", {"modules": false, "debug": true, "corejs": 3, "useBuiltIns": "usage"}]], "plugins": [["import", {"libraryName": "antd", "libraryDirectory": "es", "style": true}], "@babel/plugin-transform-runtime", "@babel/plugin-syntax-dynamic-import", "@babel/plugin-proposal-class-properties", "@babel/plugin-proposal-nullish-coalescing-operator", "@babel/plugin-proposal-optional-chaining"]}