#!/bin/bash

# 修复 kebab-case CSS 类名为 camelCase 的脚本

set -e

echo "🔄 修复 kebab-case CSS 类名..."

# 常见的 kebab-case 到 camelCase 转换
declare -A conversions=(
    ["lite-notice-btn-gray"]="liteNoticeBtnGray"
    ["fond-bond"]="fondBond"
    ["green-light"]="greenLight"
    ["red-light"]="redLight"
    ["btn-primary"]="btnPrimary"
    ["btn-secondary"]="btnSecondary"
    ["text-center"]="textCenter"
    ["text-left"]="textLeft"
    ["text-right"]="textRight"
    ["margin-top"]="marginTop"
    ["margin-bottom"]="marginBottom"
    ["padding-left"]="paddingLeft"
    ["padding-right"]="paddingRight"
    ["border-radius"]="borderRadius"
    ["background-color"]="backgroundColor"
    ["font-size"]="fontSize"
    ["font-weight"]="fontWeight"
    ["line-height"]="lineHeight"
    ["box-shadow"]="boxShadow"
    ["flex-direction"]="flexDirection"
    ["justify-content"]="justifyContent"
    ["align-items"]="alignItems"
    ["space-between"]="spaceBetween"
    ["space-around"]="spaceAround"
    ["flex-wrap"]="flexWrap"
    ["flex-grow"]="flexGrow"
    ["flex-shrink"]="flexShrink"
    ["min-width"]="minWidth"
    ["max-width"]="maxWidth"
    ["min-height"]="minHeight"
    ["max-height"]="maxHeight"
)

# 修复所有文件中的 kebab-case 类名
for kebab in "${!conversions[@]}"; do
    camel="${conversions[$kebab]}"
    echo "转换: $kebab -> $camel"
    
    # 查找并替换 styles['kebab-case'] 为 styles.camelCase
    find src -name "*.js" -o -name "*.jsx" -o -name "*.ts" -o -name "*.tsx" | while read file; do
        if grep -q "styles\[.*$kebab.*\]" "$file"; then
            echo "  修复文件: $file"
            sed -i '' "s/styles\[.*['\"]$kebab['\"].*\]/styles.$camel/g" "$file"
        fi
    done
done

echo "✅ kebab-case 类名修复完成！"

# 显示修复统计
echo "📊 修复统计："
total_files=$(find src -name "*.js" -o -name "*.jsx" -o -name "*.ts" -o -name "*.tsx" | xargs grep -l "styles\." | wc -l | tr -d ' ')
echo "- 使用 camelCase 类名的文件: $total_files 个"

echo "🧪 测试构建..."
