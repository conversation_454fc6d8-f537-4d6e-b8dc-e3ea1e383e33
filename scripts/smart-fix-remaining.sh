#!/bin/bash

# 智能修复所有剩余的 CSS 模块文件

set -e

echo "🔄 智能修复所有剩余的 CSS 模块文件..."

# 临时文件用于存储需要处理的文件列表
temp_file="/tmp/files_to_fix.txt"

# 查找所有使用 styles[...] 语法的文件
echo "🔍 查找需要修复的文件..."
find src -name "*.js" -o -name "*.jsx" -o -name "*.ts" -o -name "*.tsx" | while read file; do
    if grep -q "styles\[" "$file" || grep -q "style\[" "$file"; then
        echo "$file" >> "$temp_file"
    fi
done

# 如果临时文件存在且不为空
if [ -f "$temp_file" ] && [ -s "$temp_file" ]; then
    echo "📝 找到 $(wc -l < "$temp_file") 个需要修复的文件"
    
    # 处理每个文件
    while read -r file; do
        echo "处理文件: $file"
        
        # 检查文件是否已经有 classnames/bind 导入
        has_classnames=$(grep -c "import.*classBind\|import.*classNames.*bind" "$file" || echo "0")
        
        if [ "$has_classnames" -eq 0 ]; then
            # 添加 classnames/bind 导入
            if grep -q "import \* as styles from" "$file"; then
                # 在 styles 导入后添加
                sed -i '' '/import \* as styles from.*\.less/a\
import classBind from '\''classnames/bind'\'';\
\
const cx = classBind.bind(styles);
' "$file"
                echo "  - 添加了 classnames/bind 导入"
            elif grep -q "import \* as style from" "$file"; then
                # 在 style 导入后添加
                sed -i '' '/import \* as style from.*\.less/a\
import classBind from '\''classnames/bind'\'';\
\
const cx = classBind.bind(style);
' "$file"
                echo "  - 添加了 classnames/bind 导入"
            fi
        fi
        
        # 获取绑定函数名
        bind_func=$(grep -o "[a-zA-Z_][a-zA-Z0-9_]*\s*=.*\.bind(" "$file" | head -1 | cut -d'=' -f1 | tr -d ' ' || echo "cx")
        
        # 修复 styles['class-name'] 为 cx('class-name')
        sed -i '' "s/styles\[['\"]\([^'\"]*\)['\"]\]/${bind_func}('\1')/g" "$file"
        sed -i '' "s/style\[['\"]\([^'\"]*\)['\"]\]/${bind_func}('\1')/g" "$file"
        
        # 修复其他变量名
        for var in "stylesCommon" "styleCommon" "commonStyle" "commonStyles" "addMemberBtnStyles" "styleAside"; do
            if grep -q "$var\[" "$file"; then
                # 获取对应的绑定函数名
                var_bind_func=$(grep -o "[a-zA-Z_][a-zA-Z0-9_]*\s*=.*\.bind($var)" "$file" | head -1 | cut -d'=' -f1 | tr -d ' ' || echo "c${var}")
                sed -i '' "s/${var}\[['\"]\([^'\"]*\)['\"]\]/${var_bind_func}('\1')/g" "$file"
            fi
        done
        
        echo "  - 修复了类名使用方式"
        
    done < "$temp_file"
    
    # 清理临时文件
    rm -f "$temp_file"
    
    echo "✅ 所有文件修复完成！"
else
    echo "✅ 没有找到需要修复的文件"
fi

# 显示统计信息
echo "📊 修复统计："
total_bind_files=$(find src -name "*.js" -o -name "*.jsx" -o -name "*.ts" -o -name "*.tsx" | xargs grep -l "\.bind(" 2>/dev/null | wc -l | tr -d ' ')
echo "- 使用 classnames/bind 的文件: $total_bind_files 个"

# 检查是否还有遗漏的方括号语法
remaining_bracket=$(find src -name "*.js" -o -name "*.jsx" -o -name "*.ts" -o -name "*.tsx" | xargs grep -l "styles\[" 2>/dev/null | wc -l | tr -d ' ')
if [ "$remaining_bracket" -gt 0 ]; then
    echo "⚠️  仍有 $remaining_bracket 个文件使用方括号语法，需要手动检查"
    find src -name "*.js" -o -name "*.jsx" -o -name "*.ts" -o -name "*.tsx" | xargs grep -l "styles\[" 2>/dev/null | head -3
else
    echo "✅ 所有方括号语法都已修复"
fi

echo "🧪 测试构建..."
