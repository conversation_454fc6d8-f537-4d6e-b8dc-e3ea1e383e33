#!/bin/bash

# 迁移到 classnames/bind 方案的脚本

set -e

echo "🔄 迁移到 classnames/bind 方案..."

# 检查是否已安装 classnames
if ! npm list classnames > /dev/null 2>&1; then
    echo "📦 安装 classnames..."
    npm install classnames --save
fi

echo "📝 修改文件导入和使用方式..."

# 处理所有使用 CSS 模块的文件
find src -name "*.js" -o -name "*.jsx" -o -name "*.ts" -o -name "*.tsx" | while read file; do
    # 检查文件是否使用了 styles 导入
    if grep -q "import \* as styles from.*\.less" "$file"; then
        echo "处理文件: $file"
        
        # 检查是否已经有 classnames/bind 导入
        if ! grep -q "import classBind from 'classnames/bind'" "$file"; then
            # 在 styles 导入后添加 classnames/bind 导入和 cx 绑定
            sed -i '' '/import \* as styles from.*\.less/a\
import classBind from '\''classnames/bind'\'';\
\
const cx = classBind.bind(styles);
' "$file"
        fi
        
        # 将 styles.camelCase 替换为 cx('kebab-case')
        # 这里需要手动处理，因为自动转换比较复杂
        echo "  - 已添加 classnames/bind 导入"
    fi
done

echo "✅ 基础设置完成！"
echo ""
echo "📋 接下来需要手动修改："
echo "1. 将 styles.camelCase 改为 cx('kebab-case')"
echo "2. 将 styles['kebab-case'] 改为 cx('kebab-case')"
echo ""
echo "示例："
echo "  styles.liteNoticeBtn → cx('lite-notice-btn')"
echo "  styles['help-network'] → cx('help-network')"
echo ""
echo "🧪 测试构建..."
npm run build:prod

if [ $? -eq 0 ]; then
    echo "🎉 迁移基础设置成功！"
else
    echo "❌ 构建失败，需要手动调整代码"
fi
