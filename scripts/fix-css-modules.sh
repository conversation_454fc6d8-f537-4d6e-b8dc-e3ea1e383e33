#!/bin/bash

# CSS 模块修复脚本 - 最小改动方案
# 使用兼容的 css-loader 版本，无需修改源代码

set -e

echo "🔧 修复 CSS 模块导入问题..."

# 安装兼容的 css-loader 版本
echo "📦 安装兼容的 css-loader@2.1.1..."
npm install css-loader@2.1.1 --save-dev --legacy-peer-deps

echo "✅ CSS 模块修复完成！"

# 测试构建
echo "🧪 测试构建..."
npm run build:prod

if [ $? -eq 0 ]; then
    echo "🎉 构建成功！CSS 模块问题已解决"
    echo ""
    echo "📋 修复总结："
    echo "- ✅ 使用 css-loader@2.1.1（完全支持默认导入）"
    echo "- ✅ 保持所有现有的导入语法不变"
    echo "- ✅ 无需修改任何源代码文件"
    echo "- ✅ 最小改动，最大兼容性"
else
    echo "❌ 构建仍有问题，需要进一步排查"
fi
