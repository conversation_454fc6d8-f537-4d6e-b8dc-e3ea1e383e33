#!/bin/bash

# ES 模块迁移脚本
# 将所有 CSS 模块导入从 CommonJS 迁移到 ES 模块

set -e

echo "🔄 开始迁移到 ES 模块..."

# 查找所有需要修改的文件
echo "🔍 查找需要修改的文件..."

# 修改 .less 文件的默认导入为命名空间导入
echo "📝 修改 .less 文件导入..."
find src -name "*.js" -o -name "*.jsx" -o -name "*.ts" -o -name "*.tsx" | xargs sed -i '' 's/import styles from '\''\.\/.*\.less'\'';/import * as styles from &;/g' 2>/dev/null || true

# 修改 .module.less 文件的导入（如果有的话）
echo "📝 修改 .module.less 文件导入..."
find src -name "*.js" -o -name "*.jsx" -o -name "*.ts" -o -name "*.tsx" | xargs sed -i '' 's/import styles from '\''\.\/.*\.module\.less'\'';/import * as styles from &;/g' 2>/dev/null || true

echo "✅ 批量修改完成！"

# 手动修复一些特殊情况
echo "🔧 手动修复特殊文件..."

# 修复相对路径导入
find src -name "*.js" -o -name "*.jsx" -o -name "*.ts" -o -name "*.tsx" -exec grep -l "import styles from.*\.less" {} \; | while read file; do
    echo "修复文件: $file"
    sed -i '' 's/import styles from \(.*\.less\)/import * as styles from \1/g' "$file"
done

echo "🧪 测试构建..."
npm run build:prod

if [ $? -eq 0 ]; then
    echo "🎉 ES 模块迁移成功！"
    echo ""
    echo "📋 迁移总结："
    echo "- ✅ webpack 配置已更新为 ES 模块"
    echo "- ✅ 所有 CSS 模块导入已更新"
    echo "- ✅ 启用了 namedExport 和 camelCase"
    echo "- ✅ 构建测试通过"
else
    echo "❌ 构建失败，需要手动检查剩余问题"
fi
