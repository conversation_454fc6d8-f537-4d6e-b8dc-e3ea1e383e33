#!/bin/bash

# 修复所有剩余的默认导入文件

set -e

echo "🔄 修复所有剩余的默认导入..."

# 查找所有仍在使用默认导入的文件
echo "🔍 查找需要修复的文件..."

# 修复 import style from './file.less'
find src -name "*.js" -o -name "*.jsx" -o -name "*.ts" -o -name "*.tsx" | while read file; do
    if grep -q "import style from.*\.less" "$file"; then
        echo "修复文件: $file"
        sed -i '' 's/import style from \([^;]*\.less[^;]*\);/import * as style from \1;/g' "$file"
    fi
done

# 修复 import styles from './file.less' (如果还有遗漏的)
find src -name "*.js" -o -name "*.jsx" -o -name "*.ts" -o -name "*.tsx" | while read file; do
    if grep -q "import styles from.*\.less" "$file" && ! grep -q "import \* as styles from" "$file"; then
        echo "修复文件: $file"
        sed -i '' 's/import styles from \([^;]*\.less[^;]*\);/import * as styles from \1;/g' "$file"
    fi
done

# 修复其他可能的变量名
for var_name in "stylesCommon" "styleCommon" "commonStyle" "commonStyles"; do
    find src -name "*.js" -o -name "*.jsx" -o -name "*.ts" -o -name "*.tsx" | while read file; do
        if grep -q "import $var_name from.*\.less" "$file"; then
            echo "修复文件 ($var_name): $file"
            sed -i '' "s/import $var_name from \([^;]*\.less[^;]*\);/import * as $var_name from \1;/g" "$file"
        fi
    done
done

echo "✅ 所有默认导入修复完成！"

# 显示修复统计
echo "📊 修复统计："
total_files=$(find src -name "*.js" -o -name "*.jsx" -o -name "*.ts" -o -name "*.tsx" | xargs grep -l "import \* as.*from.*\.less" | wc -l | tr -d ' ')
echo "- 使用 ES 模块导入的文件: $total_files 个"

# 检查是否还有遗漏的默认导入
remaining=$(find src -name "*.js" -o -name "*.jsx" -o -name "*.ts" -o -name "*.tsx" | xargs grep -l "import [^*].*from.*\.less" 2>/dev/null | wc -l | tr -d ' ')
if [ "$remaining" -gt 0 ]; then
    echo "⚠️  仍有 $remaining 个文件使用默认导入，需要手动检查"
    find src -name "*.js" -o -name "*.jsx" -o -name "*.ts" -o -name "*.tsx" | xargs grep -l "import [^*].*from.*\.less" 2>/dev/null | head -5
else
    echo "✅ 所有文件都已使用 ES 模块导入"
fi

echo "🧪 测试构建..."
