#!/usr/bin/env node

/**
 * 依赖分析脚本
 * 分析项目中的重复依赖、大型依赖和未使用的依赖
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🔍 开始分析项目依赖...\n');

// 1. 分析包大小
function analyzePackageSize() {
  console.log('📦 分析包大小...');
  try {
    const result = execSync('npm ls --depth=0 --json', { encoding: 'utf8' });
    const data = JSON.parse(result);
    const dependencies = data.dependencies || {};
    
    const sizes = [];
    for (const [name, info] of Object.entries(dependencies)) {
      try {
        const packagePath = path.join('node_modules', name);
        if (fs.existsSync(packagePath)) {
          const size = getDirSize(packagePath);
          sizes.push({ name, size, version: info.version });
        }
      } catch (e) {
        // 忽略错误
      }
    }
    
    sizes.sort((a, b) => b.size - a.size);
    
    console.log('📊 最大的10个依赖包:');
    sizes.slice(0, 10).forEach((pkg, index) => {
      console.log(`${index + 1}. ${pkg.name}@${pkg.version}: ${(pkg.size / 1024 / 1024).toFixed(2)}MB`);
    });
    
    return sizes;
  } catch (error) {
    console.error('分析包大小失败:', error.message);
    return [];
  }
}

// 2. 检查重复依赖
function checkDuplicateDependencies() {
  console.log('\n🔄 检查重复依赖...');
  try {
    const result = execSync('npm ls --json', { encoding: 'utf8' });
    const data = JSON.parse(result);
    
    const allDeps = new Map();
    
    function collectDeps(deps, path = '') {
      if (!deps) return;
      
      for (const [name, info] of Object.entries(deps)) {
        const version = info.version;
        if (!allDeps.has(name)) {
          allDeps.set(name, new Set());
        }
        allDeps.get(name).add(version);
        
        if (info.dependencies) {
          collectDeps(info.dependencies, `${path}/${name}`);
        }
      }
    }
    
    collectDeps(data.dependencies);
    
    const duplicates = [];
    for (const [name, versions] of allDeps.entries()) {
      if (versions.size > 1) {
        duplicates.push({ name, versions: Array.from(versions) });
      }
    }
    
    if (duplicates.length > 0) {
      console.log('⚠️  发现重复依赖:');
      duplicates.forEach(dep => {
        console.log(`- ${dep.name}: ${dep.versions.join(', ')}`);
      });
    } else {
      console.log('✅ 未发现重复依赖');
    }
    
    return duplicates;
  } catch (error) {
    console.error('检查重复依赖失败:', error.message);
    return [];
  }
}

// 3. 分析未使用的依赖
function analyzeUnusedDependencies() {
  console.log('\n🗑️  分析可能未使用的依赖...');
  
  const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
  const dependencies = Object.keys(packageJson.dependencies || {});
  const devDependencies = Object.keys(packageJson.devDependencies || {});
  
  const potentiallyUnused = [];
  
  // 简单的文件扫描检查
  const srcFiles = getAllFiles('src', ['.js', '.jsx', '.ts', '.tsx']);
  const configFiles = getAllFiles('.', ['.js'], ['node_modules', 'dist', 'build']);
  
  const allFiles = [...srcFiles, ...configFiles];
  const fileContents = allFiles.map(file => {
    try {
      return fs.readFileSync(file, 'utf8');
    } catch {
      return '';
    }
  }).join('\n');
  
  dependencies.forEach(dep => {
    // 跳过一些明显会被使用的包
    if (['react', 'react-dom', 'antd'].includes(dep)) return;
    
    const patterns = [
      new RegExp(`require\\(['"]${dep}['"]\\)`, 'g'),
      new RegExp(`import.*from\\s+['"]${dep}['"]`, 'g'),
      new RegExp(`import\\s+['"]${dep}['"]`, 'g'),
    ];
    
    const isUsed = patterns.some(pattern => pattern.test(fileContents));
    
    if (!isUsed) {
      potentiallyUnused.push(dep);
    }
  });
  
  if (potentiallyUnused.length > 0) {
    console.log('⚠️  可能未使用的依赖 (需要手动确认):');
    potentiallyUnused.forEach(dep => {
      console.log(`- ${dep}`);
    });
  } else {
    console.log('✅ 所有依赖都在使用中');
  }
  
  return potentiallyUnused;
}

// 工具函数
function getDirSize(dirPath) {
  let size = 0;
  try {
    const files = fs.readdirSync(dirPath);
    for (const file of files) {
      const filePath = path.join(dirPath, file);
      const stats = fs.statSync(filePath);
      if (stats.isDirectory()) {
        size += getDirSize(filePath);
      } else {
        size += stats.size;
      }
    }
  } catch (e) {
    // 忽略错误
  }
  return size;
}

function getAllFiles(dir, extensions, excludeDirs = []) {
  const files = [];
  
  function scan(currentDir) {
    try {
      const items = fs.readdirSync(currentDir);
      for (const item of items) {
        const fullPath = path.join(currentDir, item);
        const stats = fs.statSync(fullPath);
        
        if (stats.isDirectory()) {
          if (!excludeDirs.some(exclude => fullPath.includes(exclude))) {
            scan(fullPath);
          }
        } else {
          const ext = path.extname(fullPath);
          if (extensions.includes(ext)) {
            files.push(fullPath);
          }
        }
      }
    } catch (e) {
      // 忽略错误
    }
  }
  
  scan(dir);
  return files;
}

// 执行分析
async function main() {
  const sizes = analyzePackageSize();
  const duplicates = checkDuplicateDependencies();
  const unused = analyzeUnusedDependencies();
  
  // 生成报告
  const report = {
    timestamp: new Date().toISOString(),
    largestPackages: sizes.slice(0, 20),
    duplicateDependencies: duplicates,
    potentiallyUnusedDependencies: unused,
    totalPackages: sizes.length,
    totalSize: sizes.reduce((sum, pkg) => sum + pkg.size, 0),
  };
  
  fs.writeFileSync('performance-logs/dependency-analysis.json', JSON.stringify(report, null, 2));
  
  console.log('\n📊 分析完成!');
  console.log(`总包数: ${report.totalPackages}`);
  console.log(`总大小: ${(report.totalSize / 1024 / 1024).toFixed(2)}MB`);
  console.log(`重复依赖: ${duplicates.length}个`);
  console.log(`可能未使用: ${unused.length}个`);
  console.log('\n详细报告已保存到: performance-logs/dependency-analysis.json');
}

main().catch(console.error);
