#!/bin/bash

# 最小化升级脚本 - 只升级最安全且必要的包
# 专注于安全修复，避免兼容性问题

set -e

echo "🔒 最小化安全升级 - 只修复安全漏洞"
echo ""

# 创建备份
echo "📦 创建备份..."
cp package.json package.json.backup

# 显示将要升级的包
echo "📋 将要升级的包："
echo ""
echo "🔒 安全修复（必须）："
echo "  axios: 0.21.1 → 1.10.0 (修复已知安全漏洞)"
echo ""
echo "⚠️  注意：只升级 axios，避免其他兼容性问题"
echo ""

read -p "是否继续升级？(y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "❌ 升级已取消"
    exit 1
fi

echo "🚀 开始最小化升级..."

# 只升级 axios - 最重要的安全修复
echo "🔒 升级 axios..."
npm install axios@^1.10.0 --legacy-peer-deps

if [ $? -ne 0 ]; then
    echo "❌ axios 升级失败，正在回滚..."
    cp package.json.backup package.json
    npm install --legacy-peer-deps > /dev/null 2>&1
    exit 1
fi

# 测试构建
echo "🧪 测试构建..."
npm run build:prod > /dev/null 2>&1

if [ $? -eq 0 ]; then
    echo "✅ 升级成功！构建测试通过"
    
    echo ""
    echo "🎉 最小化升级完成！"
    echo ""
    echo "📊 升级内容："
    echo "- ✅ axios 安全漏洞修复 (0.21.1 → 1.10.0)"
    echo ""
    echo "🔒 安全收益："
    echo "- 修复了 axios 中的已知安全漏洞"
    echo "- 提升了应用的安全性"
    echo ""
    echo "📝 下一步："
    echo "1. 测试应用功能是否正常"
    echo "2. 检查网络请求是否正常"
    echo "3. 如需更多优化，可以考虑其他方案"
    echo "4. 如有问题，恢复备份: cp package.json.backup package.json && npm install --legacy-peer-deps"
    
    # 记录升级结果
    echo "$(date '+%Y-%m-%d %H:%M:%S') - axios 安全升级成功" >> performance-logs/upgrade-history.log
    
else
    echo "❌ 构建测试失败，正在回滚..."
    cp package.json.backup package.json
    npm install --legacy-peer-deps > /dev/null 2>&1
    echo "🔄 已回滚到升级前状态"
    echo ""
    echo "💡 建议："
    echo "1. 当前项目可能存在其他配置问题"
    echo "2. 建议先使用现有的构建优化配置"
    echo "3. 可以考虑在测试环境中进行更复杂的升级"
    exit 1
fi
