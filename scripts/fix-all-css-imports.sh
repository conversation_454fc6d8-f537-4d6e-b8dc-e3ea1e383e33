#!/bin/bash

# 批量修复所有 CSS 模块导入脚本

set -e

echo "🔄 批量修复所有 CSS 模块导入..."

# 修复所有 .less 文件的导入
echo "📝 修复 .less 文件导入..."
find src -name "*.js" -o -name "*.jsx" -o -name "*.ts" -o -name "*.tsx" | while read file; do
    if grep -q "import styles from.*\.less" "$file"; then
        echo "修复文件: $file"
        sed -i '' 's/import styles from \([^;]*\.less[^;]*\);/import * as styles from \1;/g' "$file"
    fi
done

# 修复所有 .module.less 文件的导入
echo "📝 修复 .module.less 文件导入..."
find src -name "*.js" -o -name "*.jsx" -o -name "*.ts" -o -name "*.tsx" | while read file; do
    if grep -q "import styles from.*\.module\.less" "$file"; then
        echo "修复文件: $file"
        sed -i '' 's/import styles from \([^;]*\.module\.less[^;]*\);/import * as styles from \1;/g' "$file"
    fi
done

# 修复其他可能的样式导入
echo "📝 修复其他样式文件导入..."
find src -name "*.js" -o -name "*.jsx" -o -name "*.ts" -o -name "*.tsx" | while read file; do
    if grep -q "import.*styles.*from.*\.css" "$file"; then
        echo "修复 CSS 文件: $file"
        sed -i '' 's/import styles from \([^;]*\.css[^;]*\);/import * as styles from \1;/g' "$file"
    fi
done

echo "✅ 批量修复完成！"

# 显示修复的文件数量
echo "📊 修复统计："
echo "- .less 文件导入: $(find src -name "*.js" -o -name "*.jsx" -o -name "*.ts" -o -name "*.tsx" | xargs grep -l "import \* as styles from.*\.less" | wc -l | tr -d ' ') 个文件"
echo "- .module.less 文件导入: $(find src -name "*.js" -o -name "*.jsx" -o -name "*.ts" -o -name "*.tsx" | xargs grep -l "import \* as styles from.*\.module\.less" | wc -l | tr -d ' ') 个文件"

echo "🧪 测试构建..."
