#!/usr/bin/env node

/**
 * Webpack 预热脚本
 * 预编译常用模块，减少首次构建时间
 */

const webpack = require('webpack');
const path = require('path');

// 创建一个最小化的 webpack 配置用于预热
const warmupConfig = {
  mode: 'development',
  entry: {
    warmup: path.resolve(__dirname, './warmup-entry.js'),
  },
  output: {
    path: path.resolve(__dirname, '../.warmup'),
    filename: '[name].js',
  },
  resolve: {
    modules: ['node_modules'],
    extensions: ['.js', '.jsx'],
  },
  module: {
    rules: [
      {
        test: /\.(js|jsx)$/,
        exclude: /node_modules/,
        use: [
          {
            loader: 'thread-loader',
            options: {
              workers: 4,
              poolTimeout: 2000,
            },
          },
          {
            loader: 'babel-loader',
            options: {
              cacheDirectory: true,
              cacheCompression: false,
            },
          },
        ],
      },
      {
        test: /\.less$/,
        use: [
          'style-loader',
          'css-loader',
          'less-loader',
        ],
      },
    ],
  },
  plugins: [
    new webpack.DefinePlugin({
      'process.env.NODE_ENV': JSON.stringify('development'),
    }),
  ],
  cache: {
    type: 'filesystem',
  },
};

// 创建预热入口文件
const warmupEntry = `
// Webpack 预热入口文件
import React from 'react';
import ReactDOM from 'react-dom';

// 预加载常用的大型依赖
import 'antd/dist/antd.css';
import { Button, Modal, Table } from 'antd';

// 预加载一些常用的工具库
import _ from 'lodash-es';
import dayjs from 'dayjs';

console.log('Webpack warmup completed');
`;

const fs = require('fs');

// 写入预热入口文件
fs.writeFileSync(path.resolve(__dirname, './warmup-entry.js'), warmupEntry);

console.log('🔥 开始 Webpack 预热...');

const compiler = webpack(warmupConfig);

compiler.run((err, stats) => {
  if (err) {
    console.error('❌ 预热失败:', err);
    process.exit(1);
  }

  if (stats.hasErrors()) {
    console.error('❌ 预热过程中出现错误:', stats.toJson().errors);
    process.exit(1);
  }

  console.log('✅ Webpack 预热完成!');
  console.log('📊 预热统计:');
  console.log(`- 编译时间: ${stats.endTime - stats.startTime}ms`);
  console.log(`- 模块数量: ${stats.toJson().modules.length}`);

  // 清理预热文件
  try {
    fs.unlinkSync(path.resolve(__dirname, './warmup-entry.js'));
    fs.rmSync(path.resolve(__dirname, '../.warmup'), { recursive: true, force: true });
  } catch (e) {
    // 忽略清理错误
  }

  console.log('🎉 预热完成，后续构建将更快!');
});
