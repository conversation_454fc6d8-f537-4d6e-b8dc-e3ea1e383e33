#!/bin/bash

# 依赖修复脚本
# 安装所有缺少的开发依赖

set -e

echo "🔧 修复项目依赖..."

# 安装常见的缺少依赖
echo "📦 安装缺少的 webpack loaders..."
npm install --save-dev --legacy-peer-deps \
  raw-loader \
  url-loader \
  file-loader \
  html-loader \
  source-map-loader

echo "📦 安装其他可能缺少的依赖..."
npm install --save-dev --legacy-peer-deps \
  @types/node \
  @types/webpack \
  webpack-bundle-analyzer

echo "✅ 依赖修复完成！"

# 测试构建
echo "🧪 测试构建..."
npm run build:prod

if [ $? -eq 0 ]; then
    echo "🎉 构建成功！依赖问题已解决"
else
    echo "❌ 构建仍有问题，需要进一步排查"
fi
