#!/bin/bash

# 安全升级脚本 - 只升级最安全的依赖
# 避免 Babel 配置冲突，专注于安全和性能

set -e

echo "🛡️  安全依赖升级 - 只升级最安全的包"
echo ""

# 创建备份
echo "📦 创建备份..."
cp package.json package.json.backup

# 显示将要升级的包
echo "📋 将要升级的安全依赖："
echo ""
echo "🔒 安全更新（必须）："
echo "  axios: 0.21.1 → 1.10.0 (修复安全漏洞)"
echo "  core-js: 3.26.1 → 3.44.0 (安全更新)"
echo ""
echo "🚀 性能优化（兼容性好）："
echo "  css-loader: 6.11.0 → 7.1.2"
echo "  postcss-loader: 7.3.4 → 8.1.1"
echo ""

read -p "是否继续升级？(y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "❌ 升级已取消"
    exit 1
fi

echo "🚀 开始安全升级..."

# 安全更新 - 最重要
echo "🔒 安全更新..."
npm install axios@^1.10.0 core-js@^3.44.0 --legacy-peer-deps

if [ $? -ne 0 ]; then
    echo "❌ 安全更新失败，正在回滚..."
    cp package.json.backup package.json
    npm install --legacy-peer-deps > /dev/null 2>&1
    exit 1
fi

# CSS 性能优化 - 兼容性好
echo "🎨 CSS 性能优化..."
npm install --save-dev css-loader@^7.1.0 postcss-loader@^8.1.0 --legacy-peer-deps

if [ $? -ne 0 ]; then
    echo "⚠️  CSS 优化失败，但安全更新已完成"
    echo "继续进行构建测试..."
fi

# 测试构建
echo "🧪 测试构建..."
npm run build:prod > /dev/null 2>&1

if [ $? -eq 0 ]; then
    echo "✅ 升级成功！构建测试通过"
    
    # 运行性能测试
    if [ -f "performance-logs/test-optimization.sh" ]; then
        echo "📊 运行性能测试..."
        ./performance-logs/test-optimization.sh > performance-logs/safe-upgrade-result.log 2>&1 || true
        echo "📊 性能测试结果保存到: performance-logs/safe-upgrade-result.log"
    fi
    
    echo ""
    echo "🎉 安全升级完成！"
    echo ""
    echo "📊 升级内容："
    echo "- ✅ axios 安全漏洞修复"
    echo "- ✅ core-js 安全更新"
    echo "- ✅ CSS 处理性能提升（如成功）"
    echo ""
    echo "📈 预期收益:"
    echo "- 🔒 安全漏洞修复"
    echo "- 🚀 CSS 处理性能提升 5-10%"
    echo ""
    echo "📝 下一步："
    echo "1. 测试应用功能是否正常"
    echo "2. 检查是否有控制台错误"
    echo "3. 如需更多优化，可考虑其他升级方案"
    echo "4. 如有问题，恢复备份: cp package.json.backup package.json && npm install --legacy-peer-deps"
    
else
    echo "❌ 构建测试失败，正在回滚..."
    cp package.json.backup package.json
    npm install --legacy-peer-deps > /dev/null 2>&1
    echo "🔄 已回滚到升级前状态"
    echo ""
    echo "💡 建议："
    echo "1. 当前项目依赖比较复杂，建议先解决依赖冲突"
    echo "2. 可以考虑只使用已有的构建优化配置"
    echo "3. 或者联系项目维护者解决 prosemirror-commands 版本冲突"
    exit 1
fi
