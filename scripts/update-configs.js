#!/usr/bin/env node

/**
 * 配置更新脚本
 * 升级依赖后自动更新相关配置文件
 */

const fs = require('fs');
const path = require('path');

console.log('🔧 开始更新配置文件...\n');

// 1. 更新 .babelrc 配置
function updateBabelConfig() {
  console.log('📝 更新 .babelrc 配置...');
  
  const babelConfig = {
    "presets": [
      [
        "@babel/preset-react",
        {
          "runtime": "automatic",
          "development": process.env.NODE_ENV === 'development'
        }
      ],
      [
        "@babel/preset-env",
        {
          "modules": false,
          "debug": false,
          "corejs": 3,
          "useBuiltIns": "usage",
          "targets": {
            "browsers": [">0.2%", "Chrome >= 72", "Safari >= 12"]
          },
          "exclude": ["transform-typeof-symbol"]
        }
      ]
    ],
    "plugins": [
      [
        "import",
        {
          "libraryName": "antd",
          "libraryDirectory": "es",
          "style": true
        }
      ],
      [
        "@babel/plugin-transform-runtime",
        {
          "corejs": 3,
          "helpers": true,
          "regenerator": true,
          "useESModules": true
        }
      ],
      "@babel/plugin-syntax-dynamic-import",
      "@babel/plugin-proposal-class-properties",
      "@babel/plugin-proposal-nullish-coalescing-operator",
      "@babel/plugin-proposal-optional-chaining"
    ],
    "env": {
      "development": {
        "presets": [
          [
            "@babel/preset-react",
            {
              "runtime": "automatic",
              "development": true
            }
          ]
        ]
      }
    }
  };
  
  fs.writeFileSync('.babelrc', JSON.stringify(babelConfig, null, 2));
  console.log('✅ .babelrc 配置已更新');
}

// 2. 更新 postcss 配置
function updatePostcssConfig() {
  console.log('📝 更新 postcss 配置...');
  
  const postcssConfig = `module.exports = {
  plugins: [
    require('autoprefixer')({
      remove: false,
      grid: 'autoplace'
    }),
  ],
};`;
  
  fs.writeFileSync('postcss.config.js', postcssConfig);
  console.log('✅ postcss.config.js 配置已创建');
}

// 3. 检查 webpack 配置兼容性
function checkWebpackConfig() {
  console.log('🔍 检查 webpack 配置兼容性...');
  
  const commonConfigPath = 'config/webpack.common.js';
  let commonConfig = fs.readFileSync(commonConfigPath, 'utf8');
  
  // 检查是否需要更新 less-loader 配置
  if (commonConfig.includes('lessOptions')) {
    console.log('⚠️  检测到 less-loader 配置，可能需要手动调整');
    console.log('   less-loader 12.x 的配置格式可能有变化');
  }
  
  // 检查是否需要更新 sass-loader 配置
  if (commonConfig.includes('sass-loader')) {
    console.log('⚠️  检测到 sass-loader 配置，可能需要手动调整');
    console.log('   sass-loader 14.x 的配置格式可能有变化');
  }
  
  console.log('✅ webpack 配置检查完成');
}

// 4. 创建 ESLint 升级指南
function createEslintUpgradeGuide() {
  console.log('📝 创建 ESLint 升级指南...');
  
  const eslintGuide = `# ESLint 8.x 升级指南

## 主要变更

### 1. 配置文件格式
ESLint 8.x 推荐使用 .eslintrc.js 或 eslint.config.js

### 2. 规则变更
某些规则在 ESLint 8.x 中已被移除或重命名：

- \`valid-jsdoc\` → 已移除，使用 eslint-plugin-jsdoc
- \`require-jsdoc\` → 已移除，使用 eslint-plugin-jsdoc

### 3. 升级步骤

如需升级到 ESLint 8.x，请执行：

\`\`\`bash
npm install --save-dev eslint@^8.57.0
\`\`\`

然后检查并更新 .eslintrc.js 配置。

### 4. 兼容性检查

运行以下命令检查配置兼容性：

\`\`\`bash
npx eslint --print-config src/index.js
\`\`\`

## 建议

当前项目使用 ESLint 7.x，建议：
1. 先完成其他依赖升级
2. 充分测试后再考虑 ESLint 升级
3. ESLint 升级可作为独立任务进行
`;
  
  fs.writeFileSync('performance-logs/eslint-upgrade-guide.md', eslintGuide);
  console.log('✅ ESLint 升级指南已创建：performance-logs/eslint-upgrade-guide.md');
}

// 5. 更新 package.json 脚本
function updatePackageScripts() {
  console.log('📝 检查 package.json 脚本...');
  
  const packageJsonPath = 'package.json';
  const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
  
  // 添加新的脚本命令
  const newScripts = {
    "upgrade:deps": "./scripts/upgrade-dependencies.sh",
    "check:deps": "npm outdated",
    "audit:fix": "npm audit fix",
  };
  
  let updated = false;
  for (const [key, value] of Object.entries(newScripts)) {
    if (!packageJson.scripts[key]) {
      packageJson.scripts[key] = value;
      updated = true;
    }
  }
  
  if (updated) {
    fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2));
    console.log('✅ package.json 脚本已更新');
  } else {
    console.log('✅ package.json 脚本无需更新');
  }
}

// 6. 创建升级后验证清单
function createVerificationChecklist() {
  console.log('📝 创建升级后验证清单...');
  
  const checklist = `# 🔍 升级后验证清单

## 构建测试
- [ ] \`npm run build:prod\` 成功执行
- [ ] \`npm run build:qa\` 成功执行
- [ ] \`npm run build:test\` 成功执行

## 开发环境测试
- [ ] \`npm run start\` 正常启动
- [ ] \`npm run start:fast\` 正常启动
- [ ] 热更新功能正常
- [ ] 样式热更新正常

## 样式输出检查
- [ ] CSS 文件正常生成
- [ ] LESS 编译正常
- [ ] SCSS 编译正常（如有）
- [ ] 样式内容正确

## 功能测试
- [ ] 页面正常渲染
- [ ] 路由跳转正常
- [ ] 组件交互正常
- [ ] API 请求正常

## 性能测试
- [ ] 运行 \`npm run test:performance\`
- [ ] 对比升级前后构建时间
- [ ] 检查包体积变化
- [ ] 验证热更新速度

## 错误检查
- [ ] 控制台无错误信息
- [ ] 构建过程无警告
- [ ] ESLint 检查通过（如启用）

## 回滚准备
- [ ] 备份文件完整
- [ ] 了解回滚步骤
- [ ] Git 分支管理正确

## 完成标志
当所有项目都勾选完成后，升级即可认为成功。
`;
  
  fs.writeFileSync('performance-logs/verification-checklist.md', checklist);
  console.log('✅ 验证清单已创建：performance-logs/verification-checklist.md');
}

// 主函数
async function main() {
  try {
    updateBabelConfig();
    updatePostcssConfig();
    checkWebpackConfig();
    createEslintUpgradeGuide();
    updatePackageScripts();
    createVerificationChecklist();
    
    console.log('\n🎉 配置更新完成！');
    console.log('\n📋 下一步：');
    console.log('1. 运行升级脚本：./scripts/upgrade-dependencies.sh');
    console.log('2. 按照验证清单进行测试：performance-logs/verification-checklist.md');
    console.log('3. 如有问题，参考 ESLint 升级指南');
    
  } catch (error) {
    console.error('❌ 配置更新失败:', error.message);
    process.exit(1);
  }
}

main();
